@echo off
chcp 65001 >nul 2>&1
title Cursor Pro - Web Launcher
color 0A

echo.
echo ========================================
echo   Cursor Pro Web Launcher
echo ========================================
echo.

echo Starting backend API server...
set "PYTHONPATH=%cd%\src"
echo Backend will be available at: http://localhost:8080
echo.
echo Tips:
echo - Web interface will be accessible in your browser
echo - Press Ctrl+C to stop the server
echo - Server logs will appear below
echo.

echo Starting backend server...
echo Browser will open automatically after server starts...

REM 在后台启动一个延迟任务来打开浏览器
start /min cmd /c "timeout /t 8 /nobreak >nul && start http://localhost:8080"

python -m cursor_pro.core.api_server

echo.
echo Server stopped
pause
